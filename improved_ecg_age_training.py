import numpy as np
import h5py
import tensorflow as tf
from tensorflow.keras import layers, Model, regularizers, callbacks
# from scipy import signal  # 暂时不用
import os
import datetime
import gc
import math

# 启用混合精度与 XLA
tf.keras.mixed_precision.set_global_policy('mixed_float16')
try:
    tf.config.optimizer.set_jit(True)
except Exception as e:
    print(f"XLA 启用失败: {e}")

# 改进的SE残差块 - 动态reduction ratio
def improved_se_residual_block(x, filters, kernel_size=7, stride=1, dropout_rate=0.2):
    shortcut = x
    
    # 动态调整reduction ratio
    reduction_ratio = max(4, min(16, filters // 8))
    
    # 主路径
    x = layers.Conv1D(filters, kernel_size, strides=stride, padding='same', 
                     kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(dropout_rate)(x)
    
    x = layers.Conv1D(filters, kernel_size, padding='same', 
                     kernel_regularizer=regularizers.l2(1e-4))(x)
    x = layers.BatchNormalization()(x)
    
    # SE模块
    se = layers.GlobalAveragePooling1D()(x)
    se = layers.Dense(filters // reduction_ratio, activation='relu', 
                     kernel_regularizer=regularizers.l2(1e-4))(se)
    se = layers.Dense(filters, activation='sigmoid', 
                     kernel_regularizer=regularizers.l2(1e-4))(se)
    se = layers.Reshape((1, filters))(se)
    x = layers.Multiply()([x, se])
    
    # 残差连接
    if stride != 1 or shortcut.shape[-1] != filters:
        shortcut = layers.Conv1D(filters, 1, strides=stride, padding='same', 
                                kernel_regularizer=regularizers.l2(1e-4))(shortcut)
        shortcut = layers.BatchNormalization()(shortcut)
    
    x = layers.Add()([shortcut, x])
    x = layers.Activation('relu')(x)
    return x

# 位置编码层
class PositionalEncoding(layers.Layer):
    def __init__(self, max_len=5000, d_model=128, **kwargs):
        super().__init__(**kwargs)
        self.max_len = max_len
        self.d_model = d_model
        
    def build(self, input_shape):
        pe = np.zeros((self.max_len, self.d_model))
        position = np.arange(0, self.max_len).reshape(-1, 1)
        div_term = np.exp(np.arange(0, self.d_model, 2) * -(math.log(10000.0) / self.d_model))
        
        pe[:, 0::2] = np.sin(position * div_term)
        pe[:, 1::2] = np.cos(position * div_term)
        
        self.pe = self.add_weight(
            name='positional_encoding',
            shape=(self.max_len, self.d_model),
            initializer='zeros',
            trainable=False
        )
        self.pe.assign(pe)
        super().build(input_shape)
    
    def call(self, inputs):
        seq_len = tf.shape(inputs)[1]
        return inputs + self.pe[:seq_len, :]
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "max_len": self.max_len,
            "d_model": self.d_model
        })
        return config

# 改进的多头自注意力 - 添加位置编码
class ImprovedMultiHeadSelfAttention(layers.Layer):
    def __init__(self, embed_dim, num_heads=8, dropout_rate=0.1, **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        assert embed_dim % num_heads == 0
        self.projection_dim = embed_dim // num_heads
        
        self.query_dense = layers.Dense(embed_dim)
        self.key_dense = layers.Dense(embed_dim)
        self.value_dense = layers.Dense(embed_dim)
        self.combine_heads = layers.Dense(embed_dim)
        self.dropout = layers.Dropout(dropout_rate)
        self.pos_encoding = PositionalEncoding(d_model=embed_dim)
        
    def attention(self, query, key, value):
        score = tf.matmul(query, key, transpose_b=True)
        dim_key = tf.cast(tf.shape(key)[-1], score.dtype)  # 使用score的数据类型
        scaled_score = score / tf.math.sqrt(dim_key)
        weights = tf.nn.softmax(scaled_score, axis=-1)
        weights = self.dropout(weights)
        output = tf.matmul(weights, value)
        return output
    
    def separate_heads(self, x, batch_size):
        x = tf.reshape(x, (batch_size, -1, self.num_heads, self.projection_dim))
        return tf.transpose(x, perm=[0, 2, 1, 3])
    
    def call(self, inputs, training=None):
        # 添加位置编码
        inputs = self.pos_encoding(inputs)
        
        batch_size = tf.shape(inputs)[0]
        query = self.query_dense(inputs)
        key = self.key_dense(inputs)
        value = self.value_dense(inputs)
        
        query = self.separate_heads(query, batch_size)
        key = self.separate_heads(key, batch_size)
        value = self.separate_heads(value, batch_size)
        
        attention = self.attention(query, key, value)
        attention = tf.transpose(attention, perm=[0, 2, 1, 3])
        concat_attention = tf.reshape(attention, (batch_size, -1, self.embed_dim))
        output = self.combine_heads(concat_attention)
        return output
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "embed_dim": self.embed_dim,
            "num_heads": self.num_heads,
            "dropout_rate": self.dropout_rate
        })
        return config

# 简化损失函数：纯MAE，更稳定
def improved_distribution_aware_loss(y_true, y_pred):
    y_true = tf.cast(y_true, tf.float32)
    y_pred = tf.cast(y_pred, tf.float32)
    mae = tf.abs(y_true - y_pred)
    return tf.reduce_mean(mae)

# Warmup + CosineDecay学习率调度
class WarmupCosineDecay(tf.keras.optimizers.schedules.LearningRateSchedule):
    def __init__(self, initial_learning_rate, decay_steps, warmup_steps=1000, alpha=0.0):
        super().__init__()
        self.initial_learning_rate = initial_learning_rate
        self.decay_steps = decay_steps
        self.warmup_steps = warmup_steps
        self.alpha = alpha
        
    def __call__(self, step):
        # 确保数据类型一致性
        step = tf.cast(step, tf.float32)
        warmup_steps = tf.cast(self.warmup_steps, tf.float32)
        initial_lr = tf.cast(self.initial_learning_rate, tf.float32)

        # Warmup阶段
        warmup_lr = initial_lr * (step / warmup_steps)

        # CosineDecay阶段
        cosine_decay_lr = tf.keras.optimizers.schedules.CosineDecay(
            self.initial_learning_rate,
            self.decay_steps - self.warmup_steps,
            alpha=self.alpha
        )(tf.cast(step - warmup_steps, tf.int64))

        # 确保两个分支的数据类型一致
        cosine_decay_lr = tf.cast(cosine_decay_lr, tf.float32)

        return tf.where(step < warmup_steps, warmup_lr, cosine_decay_lr)
    
    def get_config(self):
        return {
            "initial_learning_rate": self.initial_learning_rate,
            "decay_steps": self.decay_steps,
            "warmup_steps": self.warmup_steps,
            "alpha": self.alpha
        }

# 内存优化的数据生成器 - 懒加载
class OptimizedECGDataGenerator(tf.keras.utils.Sequence):
    def __init__(self, file_paths, batch_size=64, is_training=True, cache_size=3):
        self.file_paths = file_paths
        self.batch_size = batch_size
        self.is_training = is_training
        self.cache_size = cache_size  # 最多同时缓存的文件数

        # 文件索引映射
        self.file_indices = []
        self.file_sample_counts = []
        # 目标长度自动推断（以首个有效样本为准）
        self.target_length = None
        # 每个文件的键名映射
        self.file_key_map = {}

        def _detect_keys(h5f):
            keys = set(list(h5f.keys()))
            # 年龄键候选
            age_candidates = ['ages', 'age', 'labels', 'label', 'targets', 'target']
            # 信号键候选
            sig_candidates = ['ecg_data', 'signals', 'signal', 'tracings', 'ecg', 'data']
            age_key = next((k for k in age_candidates if k in keys), None)
            sig_key = next((k for k in sig_candidates if k in keys), None)
            if age_key is None or sig_key is None:
                raise KeyError(f"无法在文件中检测到年龄/信号键名，文件包含: {keys}")
            return sig_key, age_key

        # 预扫描文件获取索引信息并推断长度
        for file_idx, fp in enumerate(file_paths):
            print(f"[Scan] {file_idx+1}/{len(file_paths)}: {fp}", flush=True)
            with h5py.File(fp, 'r') as f:
                sig_key, age_key = _detect_keys(f)
                self.file_key_map[file_idx] = (sig_key, age_key)
                ages = f[age_key][:]
                valid_indices = np.where((ages >= 18) & (ages <= 80))[0]
                self.file_indices.extend([(file_idx, idx) for idx in valid_indices])
                self.file_sample_counts.append(len(valid_indices))
                if self.target_length is None and valid_indices.size > 0:
                    sample = f[sig_key][valid_indices[0]]
                    self.target_length = int(sample.shape[0])

        # 若未能推断，使用回退长度
        if self.target_length is None:
            self.target_length = 5000

        self.indices = np.arange(len(self.file_indices))
        if self.is_training:
            np.random.shuffle(self.indices)

        print(f"[Keys] 映射示例: { {self.file_paths[i]: self.file_key_map[i] for i in list(self.file_key_map)[:3]} }", flush=True)
        print(f"OptimizedECGDataGenerator: 总样本数 {len(self.indices)}，目标长度 {self.target_length}")

    def get_file_handle(self, file_idx):
        """简化的文件句柄获取，避免多线程问题"""
        file_path = self.file_paths[file_idx]
        # 每次都重新打开文件，避免多线程共享句柄问题
        return h5py.File(file_path, 'r')
    
    def __len__(self):
        return int(np.ceil(len(self.indices) / self.batch_size))
    
    def __getitem__(self, idx):
        batch_indices = self.indices[idx * self.batch_size : (idx + 1) * self.batch_size]
        batch_data = []
        batch_ages = []

        for i in batch_indices:
            file_idx, sample_idx = self.file_indices[i]
            f = self.get_file_handle(file_idx)
            try:
                sig_key, age_key = self.file_key_map[file_idx]
                data = f[sig_key][sample_idx]
                age = f[age_key][sample_idx]
            finally:
                f.close()
            # 改进的预处理
            data = self._advanced_preprocess(data, self.target_length)
            batch_data.append(data)
            batch_ages.append(np.clip(age, 18.0, 80.0))

        batch_data = np.expand_dims(np.array(batch_data, dtype=np.float32), axis=-1)
        batch_ages = np.array(batch_ages, dtype=np.float32)
        return batch_data, batch_ages

    def _advanced_preprocess(self, data, target_length):
        """最简预处理，避免破坏心电特征"""
        # 长度调整 - 避免随机裁剪，保持心电完整性
        if len(data) > target_length:
            # 总是从中心裁剪，保持心电波形完整
            start_idx = (len(data) - target_length) // 2
            data = data[start_idx:start_idx + target_length]
        elif len(data) < target_length:
            # 零填充
            pad_width = target_length - len(data)
            pad_left = pad_width // 2
            pad_right = pad_width - pad_left
            data = np.pad(data, (pad_left, pad_right), 'constant', constant_values=0)

        # 最简标准化
        data_std = np.std(data)
        if data_std > 1e-6:
            data = (data - np.mean(data)) / data_std

        return data
    
    def on_epoch_end(self):
        if self.is_training:
            np.random.shuffle(self.indices)
    
    def close(self):
        """关闭所有文件句柄"""
        # 简化版本不需要缓存清理
        pass

# 改进的模型构建
# 简化的残差块
def simple_residual_block(x, filters, kernel_size=3, stride=1):
    shortcut = x

    # 主路径
    x = layers.Conv1D(filters, kernel_size, strides=stride, padding='same')(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)

    x = layers.Conv1D(filters, kernel_size, padding='same')(x)
    x = layers.BatchNormalization()(x)

    # 残差连接
    if stride != 1 or shortcut.shape[-1] != filters:
        shortcut = layers.Conv1D(filters, 1, strides=stride, padding='same')(shortcut)
        shortcut = layers.BatchNormalization()(shortcut)

    x = layers.Add()([shortcut, x])
    x = layers.Activation('relu')(x)
    return x

def create_advanced_ecg_age_model(input_shape=(5000, 1)):
    """创建简化的ECG ResNet年龄预测模型"""
    inputs = layers.Input(shape=input_shape)

    # 初始卷积层
    x = layers.Conv1D(32, 7, strides=2, padding='same')(inputs)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.MaxPooling1D(3, strides=2, padding='same')(x)

    # 残差块组
    x = simple_residual_block(x, 32, stride=1)
    x = simple_residual_block(x, 32, stride=1)

    x = simple_residual_block(x, 64, stride=2)
    x = simple_residual_block(x, 64, stride=1)

    x = simple_residual_block(x, 128, stride=2)
    x = simple_residual_block(x, 128, stride=1)

    # 全局平均池化
    x = layers.GlobalAveragePooling1D()(x)

    # 全连接层
    x = layers.Dense(128)(x)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.Dropout(0.3)(x)

    # 输出层（保持float32精度）
    outputs = layers.Dense(1, dtype="float32")(x)
    outputs = layers.Lambda(lambda t: tf.cast(18.0, tf.float32) + tf.nn.sigmoid(t) * tf.cast(62.0, tf.float32), dtype="float32")(outputs)

    model = Model(inputs=inputs, outputs=outputs)
    return model

# 梯度累积训练器
class GradientAccumulationTrainer:
    def __init__(self, model, optimizer, loss_fn, accumulation_steps=4):
        self.model = model
        self.optimizer = optimizer
        self.loss_fn = loss_fn
        self.accumulation_steps = accumulation_steps

        # 梯度累积变量
        self.accumulated_gradients = []
        for var in self.model.trainable_variables:
            self.accumulated_gradients.append(tf.Variable(tf.zeros_like(var), trainable=False))

    @tf.function
    def accumulate_gradients(self, x, y):
        """累积梯度"""
        with tf.GradientTape() as tape:
            predictions = self.model(x, training=True)
            loss = self.loss_fn(y, predictions) / self.accumulation_steps

        # 计算梯度（暂时禁用混合精度）
        gradients = tape.gradient(loss, self.model.trainable_variables)

        # 累积梯度
        for i, grad in enumerate(gradients):
            if grad is not None:
                self.accumulated_gradients[i].assign_add(grad)

        return loss

    @tf.function
    def apply_gradients(self):
        """应用累积的梯度"""
        self.optimizer.apply_gradients(zip(self.accumulated_gradients, self.model.trainable_variables))

        # 重置累积梯度
        for accumulated_grad in self.accumulated_gradients:
            accumulated_grad.assign(tf.zeros_like(accumulated_grad))

# 改进的模型编译
def compile_advanced_model(model, total_steps):
    """编译改进的模型"""
    # Warmup + CosineDecay学习率
    lr_schedule = WarmupCosineDecay(
        initial_learning_rate=5e-4,  # 降低学习率
        decay_steps=total_steps,
        warmup_steps=total_steps // 10,  # 10%的步数用于warmup
        alpha=1e-7
    )

    # 使用AdamW优化器（TF 2.15）
    optimizer = tf.keras.optimizers.AdamW(
        learning_rate=lr_schedule,
        weight_decay=1e-4,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7,
    )

    model.compile(
        optimizer=optimizer,
        loss=improved_distribution_aware_loss,
        metrics=[
            'mae',
            tf.keras.metrics.RootMeanSquaredError(name='rmse'),
            tf.keras.metrics.MeanAbsolutePercentageError(name='mape'),
            tf.keras.metrics.MeanSquaredError(name='mse')
        ],
        steps_per_execution=64
    )
    return model

# 高级回调函数
def create_advanced_callbacks(model_name="advanced_ecg_age_model"):
    """创建改进的回调函数"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"./models/{model_name}_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    callbacks_list = [
        # 早停 - 更严格的条件
        callbacks.EarlyStopping(
            monitor='val_mae',
            patience=15,
            restore_best_weights=True,
            min_delta=0.02,
            verbose=1
        ),

        # 学习率衰减 - 作为备用
        callbacks.ReduceLROnPlateau(
            monitor='val_mae',
            factor=0.3,
            patience=8,
            min_lr=1e-8,
            verbose=1,
            cooldown=3
        ),

        # 模型检查点 - 保存多个版本
        callbacks.ModelCheckpoint(
            os.path.join(model_dir, 'best_model.keras'),
            monitor='val_mae',
            save_best_only=True,
            mode='min',
            verbose=1,
            save_weights_only=False
        ),

        # 训练日志
        callbacks.CSVLogger(
            os.path.join(model_dir, 'training_log.csv'),
            separator=',',
            append=False
        ),

        # TensorBoard日志
        callbacks.TensorBoard(
            log_dir=os.path.join(model_dir, 'tensorboard'),
            histogram_freq=1,
            write_graph=True,
            write_images=False,
            update_freq='epoch'
        ),

        # 自定义评估回调
        CustomEvaluationCallback(model_dir)
    ]

    return callbacks_list, model_dir

# 自定义评估回调
class CustomEvaluationCallback(callbacks.Callback):
    def __init__(self, model_dir):
        super().__init__()
        self.model_dir = model_dir
        self.best_mae = float('inf')

    def on_epoch_end(self, epoch, logs=None):
        logs = logs or {}

        # 记录详细评估指标
        val_mae = logs.get('val_mae', 0)
        val_rmse = logs.get('val_rmse', 0)
        val_mape = logs.get('val_mape', 0)

        # 保存最佳模型的额外信息
        if val_mae < self.best_mae:
            self.best_mae = val_mae

            # 保存评估报告
            report = {
                'epoch': epoch + 1,
                'val_mae': float(val_mae),
                'val_rmse': float(val_rmse),
                'val_mape': float(val_mape),
                'timestamp': datetime.datetime.now().isoformat()
            }

            import json
            with open(os.path.join(self.model_dir, 'best_model_report.json'), 'w') as f:
                json.dump(report, f, indent=2)

        # 打印详细信息
        print(f"\nEpoch {epoch + 1} - Val MAE: {val_mae:.4f}, Val RMSE: {val_rmse:.4f}, Val MAPE: {val_mape:.2f}%")

# 主训练函数
def train_advanced_model(train_files, val_files, epochs=80, batch_size=64):
    """改进的训练主函数"""
    print("=== 开始训练改进的ECG年龄预测模型 ===")

    # 创建数据生成器
    train_gen = OptimizedECGDataGenerator(train_files, batch_size=batch_size, is_training=True)
    val_gen = OptimizedECGDataGenerator(val_files, batch_size=batch_size, is_training=False)
    # 统一验证集目标长度与训练集一致
    val_gen.target_length = train_gen.target_length

    # 计算总训练步数
    steps_per_epoch = len(train_gen)
    total_steps = steps_per_epoch * epochs

    # 创建模型（根据推断的目标长度）
    model = create_advanced_ecg_age_model(input_shape=(train_gen.target_length, 1))
    model = compile_advanced_model(model, total_steps)

    # 打印模型信息
    model.summary()
    print(f"总参数量: {model.count_params():,}")

    # 创建回调
    callbacks_list, model_dir = create_advanced_callbacks()

    try:
        # 开始训练
        history = model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=callbacks_list,
            verbose=1,
            workers=1,
            use_multiprocessing=False,
            max_queue_size=10
        )

        print(f"\n=== 训练完成！模型保存在: {model_dir} ===")
        return history, model_dir

    except KeyboardInterrupt:
        print("\n训练被用户中断")
        return None, model_dir

    finally:
        # 清理资源
        train_gen.close()
        val_gen.close()
        gc.collect()

# 使用示例
if __name__ == "__main__":
    # 设置数据目录路径
    DATA_DIR = '/opt/jupyter_notebook_workspace/data/TrainData/ecg_lead_i'

    # 自动查找所有H5文件并划分训练集和验证集
    import glob
    import random

    all_files = glob.glob(f"{DATA_DIR}/*.hdf5")
    if not all_files:
        print(f"错误：在目录 {DATA_DIR} 中没有找到任何.hdf5文件")
        exit(1)

    print(f"找到 {len(all_files)} 个数据文件")

    # 随机打乱文件列表
    random.seed(42)  # 设置随机种子确保可重复性
    random.shuffle(all_files)

    # 按8:2比例划分训练集和验证集
    split_idx = int(len(all_files) * 0.8)
    train_files = all_files[:split_idx]
    val_files = all_files[split_idx:]

    print(f"训练文件: {len(train_files)} 个")
    print(f"验证文件: {len(val_files)} 个")

    # 开始训练
    history, model_dir = train_advanced_model(
        train_files=train_files,
        val_files=val_files,
        epochs=80,
        batch_size=64
    )
